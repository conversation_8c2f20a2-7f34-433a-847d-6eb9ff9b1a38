{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}My Car Listings (Debug){% endblock %}
{% block page_title %}My Car Listings (Debug){% endblock %}

{% block dashboard_content %}
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-4">Vendor Listings - Debug Mode</h1>
        
        <div class="bg-white rounded-lg shadow p-4 mb-4">
            <h2 class="text-lg font-semibold mb-2">Debug Info</h2>
            <p>Vendor: {{ vendor.company_name|default:"No vendor" }}</p>
            <p>Total Cars: {{ total_cars }}</p>
            <p>Cars in context: {{ cars|length }}</p>
        </div>
        
        {% if cars %}
            <div class="bg-white rounded-lg shadow p-4">
                <h2 class="text-lg font-semibold mb-2">Cars (Minimal Display)</h2>
                {% for car in cars %}
                    <div class="border-b py-2">
                        <p>Car ID: {{ car.id }}</p>
                        <p>Title: {{ car.title|default:"No title" }}</p>
                        <p>Year: {{ car.year|default:"No year" }}</p>
                        <p>Price: {{ car.price|default:"No price" }}</p>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="bg-gray-100 rounded-lg p-4">
                <p>No cars to display</p>
            </div>
        {% endif %}
    </div>
{% endblock %}
