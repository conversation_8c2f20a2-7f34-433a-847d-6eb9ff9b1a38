{% load static %}
{% load core_extras %}

<!-- Enhanced Bulk Actions Bar -->
<div id="bulkActions" class="hidden bg-gradient-to-r from-harrier-red/10 to-blue-500/10 border border-harrier-red/20 rounded-xl p-5 mb-6 backdrop-blur-sm">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-br from-harrier-red to-harrier-red-dark rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-check-square text-white text-sm"></i>
            </div>
            <span class="text-harrier-dark font-bold font-montserrat">
                <span id="selectedCount">0</span> car<span id="selectedPlural">s</span> selected
            </span>
        </div>
        <div class="flex items-center space-x-3">
            <button
                type="button"
                class="inline-flex items-center px-4 py-2 text-sm bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                onclick="performBulkAction('activate')">
                <i class="fas fa-eye mr-2"></i>Show
            </button>
            <button
                type="button"
                class="inline-flex items-center px-4 py-2 text-sm bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-lg hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                onclick="performBulkAction('deactivate')">
                <i class="fas fa-eye-slash mr-2"></i>Hide
            </button>
            <button
                type="button"
                class="inline-flex items-center px-4 py-2 text-sm bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                onclick="performBulkAction('delete')">
                <i class="fas fa-trash mr-2"></i>Delete
            </button>
        </div>
    </div>
</div>

{% if cars %}
    <!-- Enhanced Table Header -->
    <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-5 border border-gray-200/50 shadow-lg mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input
                    type="checkbox"
                    id="selectAll"
                    class="h-5 w-5 text-harrier-red focus:ring-harrier-red border-gray-300 rounded-lg mr-4 transition-all duration-200"
                    onchange="selectAllCars()">
                <span class="text-sm font-bold text-harrier-dark font-montserrat">Select All Listings</span>
            </div>
            <div class="flex items-center space-x-4 text-sm text-gray-600">
                <span class="flex items-center">
                    <i class="fas fa-car mr-1 text-harrier-red"></i>
                    {{ cars|length }} listing{{ cars|length|pluralize }}
                </span>
                <span class="flex items-center">
                    <i class="fas fa-clock mr-1 text-blue-500"></i>
                    Last updated: {{ cars.0.updated_at|date:"M d, H:i"|default:"Recently" }}
                </span>
            </div>
        </div>
    </div>

    <!-- Enhanced Car Listings -->
    <div class="space-y-6" data-view="grid">
        {% for car in cars %}
            <div id="car-{{ car.id }}" class="car-card bg-gradient-to-br from-white to-gray-50 rounded-xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group">
                <div class="p-6">
                    <div class="flex items-start space-x-6">
                        <!-- Enhanced Selection Checkbox -->
                        <div class="flex-shrink-0 pt-3">
                            <input
                                type="checkbox"
                                name="selected_cars"
                                value="{{ car.id }}"
                                class="h-5 w-5 text-harrier-red focus:ring-harrier-red border-gray-300 rounded-lg transition-all duration-200"
                                onchange="updateBulkActions(); toggleCardSelection(this)">
                        </div>

                        <!-- Enhanced Car Image with Lazy Loading -->
                        <div class="flex-shrink-0 relative">
                            <div class="w-32 h-32 rounded-xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-300">
                                {% if car.main_image %}
                                    {% include 'components/lazy_image.html' with src=car.main_image.url alt=car.title class='w-full h-full object-cover group-hover:scale-110 transition-transform duration-300' placeholder_class='w-32 h-32' %}
                                {% else %}
                                    <!-- Use random product image as fallback -->
                                    {% include 'components/lazy_image.html' with src='images/products-images/p'|add:forloop.counter|add:'.jpg' alt=car.title class='w-full h-full object-cover group-hover:scale-110 transition-transform duration-300' placeholder_class='w-32 h-32' %}
                                {% endif %}
                            </div>
                            <!-- Image overlay with quick view -->
                            <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 rounded-xl flex items-center justify-center">
                                <a href="{% url 'core:car_detail' car.pk %}" class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 backdrop-blur-sm rounded-lg p-2">
                                    <i class="fas fa-eye text-harrier-red"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Enhanced Car Details -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-xl font-bold text-harrier-dark font-montserrat group-hover:text-harrier-red transition-colors">{{ car.title }}</h3>
                                        <!-- Temporarily disabled promotion badges to test recursion issue -->
                                        {% comment %}
                                        {% for badge in car.get_promotion_badges %}
                                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ badge.class }} text-white">
                                                {% if badge.type == 'featured' %}
                                                    <i class="fas fa-star mr-1"></i>
                                                {% elif badge.type == 'hot_deal' %}
                                                    <i class="fas fa-fire mr-1"></i>
                                                {% elif badge.type == 'premium' %}
                                                    <i class="fas fa-crown mr-1"></i>
                                                {% endif %}
                                                {{ badge.text }}
                                            </span>
                                        {% endfor %}
                                        {% endcomment %}
                                        {% comment %}
                                        {% if car.star_rating > 0 %}
                                            <span class="ml-2 text-yellow-500 text-sm">{{ car.get_star_display }}</span>
                                        {% endif %}
                                        {% endcomment %}
                                    </div>
                                    <p class="text-3xl font-bold text-harrier-red mb-3 font-montserrat">KSH {{ car.price|floatformat:0 }}</p>

                                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600 mb-4">
                                        <div class="flex items-center bg-white/60 rounded-lg p-2">
                                            <i class="fas fa-calendar text-harrier-red mr-2"></i>
                                            <span class="font-medium">{{ car.year }}</span>
                                        </div>
                                        <div class="flex items-center bg-white/60 rounded-lg p-2">
                                            <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                            <span class="font-medium">{{ car.mileage|floatformat:0 }} km</span>
                                        </div>
                                        <div class="flex items-center bg-white/60 rounded-lg p-2">
                                            <i class="fas fa-gas-pump text-green-500 mr-2"></i>
                                            <span class="font-medium">{{ car.fuel_type|title }}</span>
                                        </div>
                                        <div class="flex items-center bg-white/60 rounded-lg p-2">
                                            <i class="fas fa-cogs text-purple-500 mr-2"></i>
                                            <span class="font-medium">{{ car.transmission|title }}</span>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap items-center gap-2 mb-4">
                                        <!-- Enhanced Status Badge -->
                                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-xs font-bold border
                                            {% if car.is_approved %}bg-green-100 text-green-800 border-green-200
                                            {% else %}bg-yellow-100 text-yellow-800 border-yellow-200{% endif %}">
                                            {% if car.is_approved %}
                                                <i class="fas fa-check-circle mr-1"></i>Approved
                                            {% else %}
                                                <i class="fas fa-clock mr-1"></i>Pending Review
                                            {% endif %}
                                        </span>

                                        <!-- Enhanced Visibility Status -->
                                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-xs font-bold border
                                            {% if car.is_active %}bg-blue-100 text-blue-800 border-blue-200
                                            {% else %}bg-gray-100 text-gray-800 border-gray-200{% endif %}">
                                            {% if car.is_active %}
                                                <i class="fas fa-eye mr-1"></i>Live
                                            {% else %}
                                                <i class="fas fa-eye-slash mr-1"></i>Hidden
                                            {% endif %}
                                        </span>

                                        <!-- Condition Badge -->
                                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-xs font-bold bg-indigo-100 text-indigo-800 border border-indigo-200">
                                            <i class="fas fa-info-circle mr-1"></i>{{ car.get_condition_name|title }}
                                        </span>
                                    </div>

                                    <!-- Enhanced Performance Metrics -->
                                    <div class="grid grid-cols-3 gap-4 text-sm">
                                        <div class="bg-white/60 rounded-lg p-3 border border-gray-100">
                                            <div class="flex items-center">
                                                <i class="fas fa-eye text-blue-500 mr-2"></i>
                                                <div>
                                                    <div class="font-bold text-harrier-dark">{{ car.views_count|default:0 }}</div>
                                                    <div class="text-xs text-gray-600">Views</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="bg-white/60 rounded-lg p-3 border border-gray-100">
                                            <div class="flex items-center">
                                                <i class="fas fa-envelope text-yellow-500 mr-2"></i>
                                                <div>
                                                    <div class="font-bold text-harrier-dark">0</div>
                                                    <div class="text-xs text-gray-600">Inquiries</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="bg-white/60 rounded-lg p-3 border border-gray-100">
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar text-green-500 mr-2"></i>
                                                <div>
                                                    <div class="font-bold text-harrier-dark">{{ car.created_at|date:"M d" }}</div>
                                                    <div class="text-xs text-gray-600">Listed</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced Action Buttons -->
                                <div class="flex flex-col space-y-2 ml-6">
                                    <a href="{% url 'core:car_detail' car.pk %}"
                                       class="inline-flex items-center justify-center px-4 py-2 text-sm bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg font-medium">
                                        <i class="fas fa-eye mr-2"></i>View
                                    </a>

                                    <button
                                        type="button"
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm bg-gradient-to-r from-harrier-red to-harrier-red-dark text-white rounded-lg hover:from-harrier-red-dark hover:to-harrier-red transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                                        onclick="editCar({{ car.id }})">
                                        <i class="fas fa-edit mr-2"></i>Edit
                                    </button>

                                    <button
                                        type="button"
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm {% if car.is_active %}bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700{% else %}bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700{% endif %} text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                                        onclick="toggleCarStatus({{ car.id }}, '{% if car.is_active %}active{% else %}inactive{% endif %}')">
                                        {% if car.is_active %}
                                            <i class="fas fa-eye-slash mr-2"></i>Hide
                                        {% else %}
                                            <i class="fas fa-eye mr-2"></i>Show
                                        {% endif %}
                                    </button>

                                    <button
                                        type="button"
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                                        onclick="duplicateCar({{ car.id }})">
                                        <i class="fas fa-copy mr-2"></i>Copy
                                    </button>

                                    <button
                                        type="button"
                                        class="inline-flex items-center justify-center px-4 py-2 text-sm bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 transform hover:scale-105 shadow-lg font-medium"
                                        onclick="deleteCar({{ car.id }})">
                                        <i class="fas fa-trash mr-2"></i>Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Additional Info -->
                    {% if car.description %}
                        <div class="mt-6 pt-4 border-t border-gray-200/50">
                            <div class="bg-white/60 rounded-lg p-4 border border-gray-100">
                                <h4 class="text-sm font-bold text-harrier-dark mb-2 font-montserrat">Description</h4>
                                <p class="text-sm text-gray-600 font-raleway leading-relaxed">{{ car.description|truncatewords:25 }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- JavaScript for enhanced interactions -->
    <script>
    function toggleCardSelection(checkbox) {
        const carCard = checkbox.closest('.car-card');
        if (carCard) {
            if (checkbox.checked) {
                carCard.classList.add('ring-2', 'ring-harrier-red', 'ring-opacity-50', 'bg-harrier-red/5');
            } else {
                carCard.classList.remove('ring-2', 'ring-harrier-red', 'ring-opacity-50', 'bg-harrier-red/5');
            }
        }

        // Update selected count display
        const selectedCount = document.querySelectorAll('input[name="selected_cars"]:checked').length;
        const selectedCountElement = document.getElementById('selectedCount');
        const selectedPluralElement = document.getElementById('selectedPlural');

        if (selectedCountElement) {
            selectedCountElement.textContent = selectedCount;
        }
        if (selectedPluralElement) {
            selectedPluralElement.textContent = selectedCount === 1 ? '' : 's';
        }
    }
    </script>

    <!-- Enhanced Pagination -->
    {% if cars.has_other_pages %}
        <div class="mt-12 flex items-center justify-center">
            <nav class="flex items-center space-x-2 bg-white rounded-xl p-2 shadow-lg border border-gray-200/50">
                {% if cars.has_previous %}
                    <a
                        href="?page={{ cars.previous_page_number }}"
                        class="inline-flex items-center px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg hover:from-gray-200 hover:to-gray-300 text-gray-700 transition-all duration-200 font-medium"
                        hx-get="?page={{ cars.previous_page_number }}"
                        hx-target="#listings-container">
                        <i class="fas fa-chevron-left mr-2"></i>Previous
                    </a>
                {% endif %}

                {% for num in cars.paginator.page_range %}
                    {% if cars.number == num %}
                        <span class="px-4 py-2 text-sm bg-gradient-to-r from-harrier-red to-harrier-red-dark text-white rounded-lg font-bold shadow-lg">{{ num }}</span>
                    {% elif num > cars.number|add:'-3' and num < cars.number|add:'3' %}
                        <a
                            href="?page={{ num }}"
                            class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gradient-to-r hover:from-harrier-red/10 hover:to-harrier-red/20 hover:border-harrier-red/30 text-gray-700 hover:text-harrier-red transition-all duration-200 font-medium"
                            hx-get="?page={{ num }}"
                            hx-target="#listings-container">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if cars.has_next %}
                    <a
                        href="?page={{ cars.next_page_number }}"
                        class="inline-flex items-center px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg hover:from-gray-200 hover:to-gray-300 text-gray-700 transition-all duration-200 font-medium"
                        hx-get="?page={{ cars.next_page_number }}"
                        hx-target="#listings-container">
                        Next<i class="fas fa-chevron-right ml-2"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    {% endif %}
{% else %}
    <!-- Enhanced Empty State -->
    <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-gray-200/50 overflow-hidden">
        <div class="p-16 text-center">
            <div class="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-lg">
                <i class="fas fa-car text-6xl text-gray-400"></i>
            </div>
            <h3 class="text-2xl font-bold text-harrier-dark mb-4 font-montserrat">No Car Listings Found</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto font-raleway leading-relaxed">You haven't listed any cars yet, or no cars match your current filters. Start building your inventory today!</p>

            <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                <a href="{% url 'core:sell_car' %}" class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-harrier-red to-harrier-red-dark text-white rounded-lg font-bold hover:from-harrier-red-dark hover:to-harrier-red transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl font-montserrat">
                    <i class="fas fa-plus mr-3"></i>List Your First Car
                </a>

                <button onclick="location.reload()" class="inline-flex items-center px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow-sm">
                    <i class="fas fa-refresh mr-2"></i>Refresh
                </button>
            </div>

            <!-- Quick Tips -->
            <div class="mt-12 bg-white/60 rounded-lg p-6 border border-gray-100 max-w-2xl mx-auto">
                <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat">Quick Tips to Get Started</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div class="flex items-start">
                        <i class="fas fa-camera text-harrier-red mr-3 mt-1"></i>
                        <div>
                            <div class="font-semibold text-harrier-dark">High-Quality Photos</div>
                            <div class="text-gray-600">Upload clear, well-lit images</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-edit text-blue-500 mr-3 mt-1"></i>
                        <div>
                            <div class="font-semibold text-harrier-dark">Detailed Description</div>
                            <div class="text-gray-600">Include all important features</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-tag text-green-500 mr-3 mt-1"></i>
                        <div>
                            <div class="font-semibold text-harrier-dark">Competitive Pricing</div>
                            <div class="text-gray-600">Research market prices</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
